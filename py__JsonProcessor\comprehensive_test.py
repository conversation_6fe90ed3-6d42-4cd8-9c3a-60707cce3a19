#!/usr/bin/env python3
"""
Comprehensive test suite for JsonProcessor.

This file contains extensive testing scenarios that were moved out of the
main module to maintain code clarity and adherence to guidelines.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from json_processor import JsonProcessor


def test_comprehensive_json():
    """Test with the original complex JSON layout data."""
    
    # Complex test JSON with trailing commas and comments
    complex_json = """
    {
      // This is a comment
      "uuid": "{6DE24C5A-7680-4A9C-AEDF-8460C886F143}",
      "name": "layout-0006-d--large",
      "type": "canvas",
      "info": {
        "ref-width": 3072,
        "ref-height": 1680,
        "zones": [
          {"X": 0, "Y": 0, "width": 591, "height": 420},
          {"X": 0, "Y": 0, "width": 591, "height": 1206},
          {"X": 0, "Y": 897, "width": 591, "height": 783},
          {"X": 0, "Y": 1206, "width": 591, "height": 474},
          {"X": 0, "Y": 0, "width": 591, "height": 1680},
          {"X": 0, "Y": 0, "width": 1109, "height": 420},
          {"X": 0, "Y": 0, "width": 1109, "height": 1206},
          {"X": 0, "Y": 897, "width": 1109, "height": 783},
          {"X": 0, "Y": 1206, "width": 1109, "height": 474},
          {"X": 0, "Y": 0, "width": 1111, "height": 1680},
          {"X": 1109, "Y": 0, "width": 805, "height": 420},
          {"X": 1109, "Y": 0, "width": 805, "height": 1206},
          {"X": 1109, "Y": 897, "width": 805, "height": 783},
          {"X": 1109, "Y": 1206, "width": 805, "height": 474},
          {"X": 1109, "Y": 0, "width": 805, "height": 1680},
          {"X": 1109, "Y": 0, "width": 1308, "height": 420},
          {"X": 1109, "Y": 0, "width": 1308, "height": 1206},
          {"X": 1109, "Y": 897, "width": 1308, "height": 783},
          {"X": 1109, "Y": 1206, "width": 1308, "height": 474},
          {"X": 1109, "Y": 0, "width": 1308, "height": 1680},
          {"X": 1914, "Y": 0, "width": 1158, "height": 420},
          {"X": 1914, "Y": 0, "width": 1158, "height": 1206},
          {"X": 1914, "Y": 897, "width": 1158, "height": 783},
          {"X": 1914, "Y": 1206, "width": 1158, "height": 474},
          {"X": 1914, "Y": 0, "width": 1158, "height": 1680},
          {"X": 2481, "Y": 0, "width": 591, "height": 420},
          {"X": 2481, "Y": 0, "width": 591, "height": 1206},
          {"X": 2481, "Y": 897, "width": 591, "height": 783},
          {"X": 2481, "Y": 1206, "width": 591, "height": 474},
          {"X": 2481, "Y": 0, "width": 591, "height": 1680},
          {"X": 0, "Y": 0, "width": 3072, "height": 397},
          {"X": 0, "Y": 1219, "width": 3072, "height": 461},
          {"X": 0, "Y": 1608, "width": 3072, "height": 72}, // trailing comma here
        ],
        "sensitivity-radius": 20, // another trailing comma
      }, // and here
    } // final trailing comma
    """

    print("=== Comprehensive JSON Test ===")
    try:
        processor = JsonProcessor()
        data = processor.parse(complex_json)

        print("✓ Successfully parsed complex JSON with trailing commas and comments")
        print(f"Layout name: {data['name']}")
        print(f"Canvas type: {data['type']}")
        print(f"Number of zones: {len(data['info']['zones'])}")
        print(f"Canvas dimensions: {data['info']['ref-width']}x{data['info']['ref-height']}")

        # Demonstrate formatting
        print("\n=== Formatted Output (first 3 zones) ===")
        sample_data = {
            "name": data["name"],
            "zones_sample": data["info"]["zones"][:3]
        }
        formatted = processor.format(sample_data)
        print(formatted)

    except Exception as e:
        print(f"✗ Processing failed: {e}")


def test_error_scenarios():
    """Test various error conditions."""
    processor = JsonProcessor()
    
    print("\n=== Error Handling Tests ===")
    
    # Test 1: Invalid JSON
    try:
        processor.parse("invalid json {")
        print("✗ Should have failed on invalid JSON")
    except ValueError as e:
        print(f"✓ Invalid JSON caught: {e}")
    
    # Test 2: Empty string
    try:
        processor.parse("")
        print("✗ Should have failed on empty string")
    except ValueError as e:
        print(f"✓ Empty string caught: {e}")
    
    # Test 3: Non-string input
    try:
        processor.parse(123)
        print("✗ Should have failed on non-string input")
    except TypeError as e:
        print(f"✓ Non-string input caught: {e}")
    
    # Test 4: Valid JSON with trailing commas
    try:
        result = processor.parse('{"test": "value",}')
        print(f"✓ Valid JSON with trailing comma parsed: {result}")
    except Exception as e:
        print(f"✗ Valid JSON failed: {e}")


if __name__ == "__main__":
    test_comprehensive_json()
    test_error_scenarios()
