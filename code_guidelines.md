# Requirements
- Always clean up after yourself, and do it in a way that properly accounts (and cohesively adapts to) the existing structure of the codebase.
- If you decide to create a test for verifying the newly added functionality, always make sure you first check the codebase for existing tests.

# Code Guidelines

## Core Principles
- Follow patterns derived from the principles of *inherent* clarity, structure, simplicity, elegance, precision, and intent.
- Maintain inherent simplicity while providing powerful functionality.
- Eembrace the unassailable truth that simplicity effortlessly conquers chaos. Chase transformative, high-impact innovations that demand minimal disruption but yield profound, elegant improvements, driven by an unwavering quest for excellence.
- Recognize that the chosen solution triumphs over any method merely employed; seek that singular, universally resonant breakthrough that weaves perfect contextual integrity with elite execution logic, unlocking a cascade of effortless, world-class impact.

## General Principles
- Aim for simplicity, clarity, and maintainability in all project aspects
- Favor composition over inheritance when applicable
- Prioritize readability and understandability for future developers
- Ensure all components have a single responsibility
- Coding standards that promote simplicity and maintainability
- Document only integral decisions in a highly condensed form

## Code Organization
- Evaluate the existing codebase structure and identify patterns and anti-patterns
- Consolidate related functionality into cohesive modules
- Minimize dependencies between unrelated components
- Optimize for developer ergonomics and intuitive navigation
- Balance file granularity with overall system comprehensibility