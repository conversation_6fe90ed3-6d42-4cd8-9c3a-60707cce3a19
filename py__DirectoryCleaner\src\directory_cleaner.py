#!/usr/bin/env python3
"""
Directory Cleaner - Removes empty directories and specified files.

Usage:
  python directory_cleaner.py [PATH] [--dry-run] [--yes] [--prompt]

Examples:
  python directory_cleaner.py                   # Clean current directory (with prompts)
  python directory_cleaner.py /some/path        # Clean /some/path (with prompts)
  python directory_cleaner.py --dry-run         # Simulate removals without deleting
  python directory_cleaner.py --yes             # Remove without confirming each directory
  python directory_cleaner.py --prompt          # Enter interactive mode for directory & flags
"""

import os
import sys
import argparse
import shutil
from pathlib import Path

# Rich imports for console interactions
from rich.console import Console
from rich.prompt import Prompt, Confirm
from rich.panel import Panel
from rich.table import Table
from rich.box import ROUNDED

# =============================================================================
# Configuration
# =============================================================================
class Config:
    """
    Holds default settings for the Directory Cleaner.
    """
    USE_DEFAULT_SETTINGS = True
    DEFAULT_DRY_RUN = False
    DEFAULT_AUTO_CONFIRM = True
    DEFAULT_TARGET_PATH = "."

    # Files to delete during cleanup
    FILES_TO_DELETE = [
        "youtube_downloader.log",
        "app.log.yml",
        ".new_hashes.py",
        ".original_hashes.py",
        "video_audio_combiner.log",
        "__pycache__",
        # "error.log",
        # "temp.txt",
        # "cache.tmp",
        # ".DS_Store",
        # "Thumbs.db",
        # "desktop.ini"
    ]

    # TODO: Directories to delete during cleanup
    DIRS_TO_DELETE = [
        "__pycache__",
    ]

# =============================================================================
# Argument Handler
# =============================================================================
class ArgumentHandler:
    """Handles CLI argument parsing and optional prompting."""

    def __init__(self):
        self.parser = self.parse_arguments()

    @staticmethod
    def parse_arguments():
        parser = argparse.ArgumentParser(
            description="Remove empty directories and specified files from a specified path."
        )
        parser.add_argument(
            "path",
            nargs="?",
            default=None,
            help="Path to the directory you wish to clean."
        )
        parser.add_argument(
            "--dry-run",
            action="store_true",
            default=Config.DEFAULT_DRY_RUN,
            help="Simulate the removal process (no directories will actually be deleted)."
        )
        parser.add_argument(
            "-y", "--yes",
            action="store_true",
            default=Config.DEFAULT_AUTO_CONFIRM,
            help="Automatically confirm all deletions without prompting."
        )
        parser.add_argument(
            "--prompt",
            action="store_true",
            help="Enter interactive mode to specify path and settings."
        )
        return parser

    def get_arguments(self):
        return self.parser.parse_args()

    def prompt_for_missing_arguments(self, args):
        console = Console()

        def print_section(title):
            console.print(f"\n[bold blue] --- {title} ---[/bold blue]", highlight=False)

        if args.prompt:
            print_section("Default Settings")
            use_defaults = Confirm.ask("Use default settings?", default=Config.USE_DEFAULT_SETTINGS)

            if not use_defaults:
                # Prompt for target path
                print_section("Directory")
                current_path = args.path or Config.DEFAULT_TARGET_PATH
                args.path = Prompt.ask("Directory to clean", default=current_path).strip()

                # Prompt for dry-run
                print_section("Dry Run Mode")
                args.dry_run = Confirm.ask("Run in dry-run mode? (no actual deletion)", default=Config.DEFAULT_DRY_RUN)

                # Prompt for auto-confirm
                print_section("Auto-confirm Deletions")
                args.yes = Confirm.ask("Auto-confirm deletions? (no per-file/directory prompt)", default=Config.DEFAULT_AUTO_CONFIRM)
            else:
                # Use defaults if none provided
                args.path = args.path or Config.DEFAULT_TARGET_PATH
                args.dry_run = args.dry_run if hasattr(args, 'dry_run') and args.dry_run else Config.DEFAULT_DRY_RUN
                args.yes = args.yes if hasattr(args, 'yes') and args.yes else Config.DEFAULT_AUTO_CONFIRM

        # Validation
        target_path = Path(args.path or Config.DEFAULT_TARGET_PATH)
        if not target_path.exists() or not target_path.is_dir():
            console.print(f"[red]Error:[/] Directory '{target_path}' does not exist or is not a directory.")
            sys.exit(1)

        return args

# =============================================================================
# Directory Cleaner Core
# =============================================================================
class DirectoryCleaner:
    """Core directory cleaning functionality."""

    def __init__(self, target_path: Path, dry_run: bool = False, auto_confirm: bool = True):
        self.target_path = target_path
        self.dry_run = dry_run
        self.auto_confirm = auto_confirm
        self.console = Console()

    def confirm(self, message: str) -> bool:
        """
        Ask the user for confirmation unless auto-confirm is enabled.
        Returns True if confirmed, otherwise False.
        """
        if self.auto_confirm:
            return True
        return Confirm.ask(f"{message}", default=False)

    def remove_specified_files(self) -> tuple[int, int]:
        """
        Traverse the directory and remove files that match the FILES_TO_DELETE list.
        Returns a tuple of (matched_count, removed_count).
        """
        matched, removed = 0, 0

        for root, dirs, files in os.walk(self.target_path):
            for file_name in files:
                if file_name in Config.FILES_TO_DELETE:
                    file_path = Path(root) / file_name
                    matched += 1
                    if self.dry_run:
                        self.console.print(f"[yellow][DRY RUN][/] Found file to delete: [cyan]{file_path}[/]")
                    else:
                        # Prompt for confirmation (unless auto-confirm is enabled)
                        if self.confirm(f"Delete file: [cyan]{file_path}[/]?"):
                            try:
                                file_path.unlink()
                                removed += 1
                                self.console.print(f"[green]✓[/] Removed: [cyan]{file_path}[/]")
                            except Exception as e:
                                self.console.print(f"[red]✗[/] Error removing [cyan]{file_path}[/]: {e}")
                        else:
                            self.console.print(f"[yellow]⊘[/] Skipped: [cyan]{file_path}[/]")

        return matched, removed

    def remove_specified_directories(self) -> tuple[int, int]:
        """
        Traverse the directory and remove directories that match the DIRS_TO_DELETE list.
        Returns a tuple of (matched_count, removed_count).
        """
        matched, removed = 0, 0

        # Use topdown=False to process subdirectories before parent directories
        for root, dirs, files in os.walk(self.target_path, topdown=False):
            for dir_name in dirs:
                if dir_name in Config.DIRS_TO_DELETE:
                    dir_path = Path(root) / dir_name
                    matched += 1
                    if self.dry_run:
                        self.console.print(f"[yellow][DRY RUN][/] Found directory to delete: [cyan]{dir_path}[/]")
                    else:
                        # Prompt for confirmation (unless auto-confirm is enabled)
                        if self.confirm(f"Delete directory: [cyan]{dir_path}[/]?"):
                            try:
                                # Use shutil.rmtree to remove directory and all its contents
                                shutil.rmtree(dir_path)
                                removed += 1
                                self.console.print(f"[green]✓[/] Removed: [cyan]{dir_path}[/]")
                            except Exception as e:
                                self.console.print(f"[red]✗[/] Error removing [cyan]{dir_path}[/]: {e}")
                        else:
                            self.console.print(f"[yellow]⊘[/] Skipped: [cyan]{dir_path}[/]")

        return matched, removed


    def remove_empty_directories(self) -> tuple[int, int]:
        """
        Traverse the directory bottom-up, detect empty folders, and remove them
        if confirmed. Returns a tuple of (matched_count, removed_count).
        """
        matched, removed = 0, 0

        # Bottom-up ensures we remove empties in nested folders first
        for root, dirs, files in os.walk(self.target_path, topdown=False):
            path_obj = Path(root)
            try:
                # If directory is empty
                if not any(path_obj.iterdir()):
                    matched += 1
                    if self.dry_run:
                        self.console.print(f"[yellow][DRY RUN][/] Found empty directory: [cyan]{path_obj}[/]")
                    else:
                        # Prompt for confirmation (unless auto-confirm is enabled)
                        if self.confirm(f"Delete empty directory: [cyan]{path_obj}[/]?"):
                            path_obj.rmdir()
                            removed += 1
                            self.console.print(f"[green]✓[/] Removed: [cyan]{path_obj}[/]")
                        else:
                            self.console.print(f"[yellow]⊘[/] Skipped: [cyan]{path_obj}[/]")
            except Exception as e:
                self.console.print(f"[red]✗[/] Error accessing [cyan]{path_obj}[/]: {e}")

        return matched, removed

    def display_summary(self, files_matched: int, files_removed: int,
                       specified_dirs_matched: int, specified_dirs_removed: int,
                       empty_dirs_matched: int, empty_dirs_removed: int):
        """Display a formatted summary of the cleaning operation."""

        # Create summary table
        table = Table(
            title="Directory Cleaning Summary",
            show_header=True,
            header_style="bold blue",
            box=ROUNDED
        )
        table.add_column("Category", style="cyan", width=20)
        table.add_column("Found", style="yellow", justify="center")
        table.add_column("Processed", style="green", justify="center")

        table.add_row("Files to delete", str(files_matched), str(files_removed))
        table.add_row("Specified directories", str(specified_dirs_matched), str(specified_dirs_removed))
        table.add_row("Empty directories", str(empty_dirs_matched), str(empty_dirs_removed))

        self.console.print("\n")
        self.console.print(Panel(table, border_style="blue"))

        if self.dry_run:
            self.console.print(f"\n[yellow]Note:[/] This was a dry run. No actual changes were made.")
        else:
            total_removed = files_removed + specified_dirs_removed + empty_dirs_removed
            if total_removed > 0:
                self.console.print(f"\n[green]✓[/] Successfully cleaned [bold]{total_removed}[/] items.")
            else:
                self.console.print(f"\n[blue]ℹ[/] No items needed cleaning.")

    def run_cleaning(self) -> tuple[int, int, int, int, int, int]:
        """Execute the cleaning process and return statistics."""
        self.console.print(f"\n[bold blue]Scanning directory:[/] [cyan]{self.target_path}[/]")

        # Display files that will be targeted
        files_list = ", ".join(Config.FILES_TO_DELETE)
        self.console.print(f"[bold blue]Target files:[/] {files_list}")

        # Display directories that will be targeted
        dirs_list = ", ".join(Config.DIRS_TO_DELETE)
        self.console.print(f"[bold blue]Target directories:[/] {dirs_list}")

        if self.dry_run:
            self.console.print("[yellow]Running in DRY RUN mode. No files or directories will be deleted.[/]")
        if self.auto_confirm:
            self.console.print("[blue]Auto-confirm mode enabled. Deletions will proceed without prompting.[/]")

        self.console.print()

        # Remove specified files first
        files_matched, files_removed = self.remove_specified_files()

        # Remove specified directories
        specified_dirs_matched, specified_dirs_removed = self.remove_specified_directories()

        # Then remove empty directories
        empty_dirs_matched, empty_dirs_removed = self.remove_empty_directories()

        return files_matched, files_removed, specified_dirs_matched, specified_dirs_removed, empty_dirs_matched, empty_dirs_removed

# =============================================================================
# Main App Class
# =============================================================================
class DirectoryCleanerApp:
    """
    Encapsulates the CLI workflow for directory cleaning:
      1) Parse & prompt for Arguments
      2) Execute cleaning process
      3) Display results
    """

    def __init__(self):
        self.arg_handler = ArgumentHandler()
        self.args = None
        self.console = Console()

    def run(self) -> None:
        """Main execution flow."""
        # 1) Parse & prompt for arguments
        self.args = self.arg_handler.get_arguments()
        self.args = self.arg_handler.prompt_for_missing_arguments(self.args)

        # 2) Use the final path value, defaulting to current directory if still None
        target_path = Path(self.args.path or Config.DEFAULT_TARGET_PATH)

        # 3) Create cleaner instance and run
        cleaner = DirectoryCleaner(
            target_path=target_path,
            dry_run=self.args.dry_run,
            auto_confirm=self.args.yes
        )

        # 4) Execute cleaning and get results
        (files_matched, files_removed, specified_dirs_matched,
         specified_dirs_removed, empty_dirs_matched, empty_dirs_removed) = cleaner.run_cleaning()

        # 5) Display summary
        cleaner.display_summary(files_matched, files_removed, specified_dirs_matched,
                               specified_dirs_removed, empty_dirs_matched, empty_dirs_removed)

        # 6) Wait for user to press Enter before exiting
        self.console.print("\n[dim]Press Enter to exit...[/]")
        input()


# =============================================================================
# Execution entrypoint
# =============================================================================
def main() -> None:
    """Main entry point of the utility."""
    app = DirectoryCleanerApp()
    app.run()


if __name__ == "__main__":
    main()
