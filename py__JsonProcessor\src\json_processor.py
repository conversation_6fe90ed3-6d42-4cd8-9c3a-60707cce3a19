import re
import json

def remove_trailing_commas(json_string):
    """
    Remove trailing commas and comments from JSON string using regex.

    Note: This is a basic implementation. For production use, consider
    using the 'json5' library: pip install json5

    Args:
        json_string (str): JSON string with potential trailing commas and comments

    Returns:
        str: Cleaned JSON string
    """
    # Remove single-line comments (// ...)
    json_string = re.sub(r'//.*?(?=\n|$)', '', json_string)

    # Remove multi-line comments (/* ... */)
    json_string = re.sub(r'/\*.*?\*/', '', json_string, flags=re.DOTALL)

    # Remove trailing commas before closing braces and brackets
    json_string = re.sub(r',(\s*[}\]])', r'\1', json_string)

    return json_string

def remove_trailing_commas_with_json5(json_string):
    """
    Parse JSON with trailing commas and comments using the json5 library.

    This is the recommended approach for production use.
    Install with: pip install json5

    Args:
        json_string (str): JSON string with potential trailing commas and comments

    Returns:
        dict/list: Parsed JSON object
    """
    try:
        import json5
        return json5.loads(json_string)
    except ImportError:
        print("json5 library not installed. Install with: pip install json5")
        print("Falling back to regex-based cleaning...")
        cleaned = remove_trailing_commas(json_string)
        return json.loads(cleaned)
    except Exception as e:
        print(f"Error parsing JSON: {e}")
        print("Falling back to regex-based cleaning...")
        cleaned = remove_trailing_commas(json_string)
        return json.loads(cleaned)

def main():
    """Example usage demonstrating both methods."""

    # Test JSON with trailing commas and comments
    invalid_json = """
    {
      // This is a comment
      "uuid": "{6DE24C5A-7680-4A9C-AEDF-8460C886F143}",
      "name": "layout-0006-d--large",
      "type": "canvas",
      "info": {
        "ref-width": 3072,
        "ref-height": 1680,
        "zones": [
          {"X": 0, "Y": 0, "width": 591, "height": 420},
          {"X": 0, "Y": 0, "width": 591, "height": 1206},
          {"X": 0, "Y": 897, "width": 591, "height": 783},
          {"X": 0, "Y": 1206, "width": 591, "height": 474},
          {"X": 0, "Y": 0, "width": 591, "height": 1680},
          {"X": 0, "Y": 0, "width": 1109, "height": 420},
          {"X": 0, "Y": 0, "width": 1109, "height": 1206},
          {"X": 0, "Y": 897, "width": 1109, "height": 783},
          {"X": 0, "Y": 1206, "width": 1109, "height": 474},
          {"X": 0, "Y": 0, "width": 1111, "height": 1680},
          {"X": 1109, "Y": 0, "width": 805, "height": 420},
          {"X": 1109, "Y": 0, "width": 805, "height": 1206},
          {"X": 1109, "Y": 897, "width": 805, "height": 783},
          {"X": 1109, "Y": 1206, "width": 805, "height": 474},
          {"X": 1109, "Y": 0, "width": 805, "height": 1680},
          {"X": 1109, "Y": 0, "width": 1308, "height": 420},
          {"X": 1109, "Y": 0, "width": 1308, "height": 1206},
          {"X": 1109, "Y": 897, "width": 1308, "height": 783},
          {"X": 1109, "Y": 1206, "width": 1308, "height": 474},
          {"X": 1109, "Y": 0, "width": 1308, "height": 1680},
          {"X": 1914, "Y": 0, "width": 1158, "height": 420},
          {"X": 1914, "Y": 0, "width": 1158, "height": 1206},
          {"X": 1914, "Y": 897, "width": 1158, "height": 783},
          {"X": 1914, "Y": 1206, "width": 1158, "height": 474},
          {"X": 1914, "Y": 0, "width": 1158, "height": 1680},
          {"X": 2481, "Y": 0, "width": 591, "height": 420},
          {"X": 2481, "Y": 0, "width": 591, "height": 1206},
          {"X": 2481, "Y": 897, "width": 591, "height": 783},
          {"X": 2481, "Y": 1206, "width": 591, "height": 474},
          {"X": 2481, "Y": 0, "width": 591, "height": 1680},
          {"X": 0, "Y": 0, "width": 3072, "height": 397},
          {"X": 0, "Y": 1219, "width": 3072, "height": 461},
          {"X": 0, "Y": 1608, "width": 3072, "height": 72}, // trailing comma here
        ],
        "sensitivity-radius": 20, // another trailing comma
      }, // and here
    } // final trailing comma
    """

    print("=== Method 1: Using regex-based cleaning ===")
    try:
        cleaned_json = remove_trailing_commas(invalid_json)
        data = json.loads(cleaned_json)
        print("✓ Successfully parsed with regex method")
        print(f"Parsed {len(data['info']['zones'])} zones")
    except Exception as e:
        print(f"✗ Regex method failed: {e}")

    print("\n=== Method 2: Using json5 library (recommended) ===")
    try:
        data = remove_trailing_commas_with_json5(invalid_json)
        if data and isinstance(data, dict):
            print("✓ Successfully parsed with json5 method")
            print(f"Parsed {len(data['info']['zones'])} zones")
            print(f"Layout name: {data['name']}")
        else:
            print("✗ json5 method returned invalid data")
    except Exception as e:
        print(f"✗ json5 method failed: {e}")

if __name__ == "__main__":
    main()
