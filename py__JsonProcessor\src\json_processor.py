#!/usr/bin/env python3
"""
JSON Processor - Clean and parse JSON with trailing commas and comments.

This utility provides a clean, simple interface for processing JSON5-style
content using the json5 library as the primary method.
"""

import json
from typing import Union, Dict, List, Any

try:
    import json5
except ImportError:
    raise ImportError(
        "json5 library is required. Install with: pip install json5"
    )


# =============================================================================
# Core JSON Processing
# =============================================================================
class JsonProcessor:
    """
    Handles JSON processing with trailing commas and comments.

    Follows the principle of inherent simplicity by using json5 as the
    single, reliable method for parsing non-standard JSON.
    """

    @staticmethod
    def parse(json_string: str) -> Union[Dict[str, Any], List[Any], Any]:
        """
        Parse JSON string with trailing commas and comments.

        Args:
            json_string: JSON string that may contain trailing commas and comments

        Returns:
            Parsed JSON object (dict, list, or primitive)

        Raises:
            ValueError: If the JSON string cannot be parsed
        """
        if not isinstance(json_string, str):
            raise TypeError("Input must be a string")

        if not json_string.strip():
            raise ValueError("Input string is empty or contains only whitespace")

        try:
            return json5.loads(json_string)
        except Exception as e:
            raise ValueError(f"Failed to parse JSON: {e}")

    @staticmethod
    def format(data: Union[Dict, List, Any], indent: int = 2) -> str:
        """
        Format parsed data as clean JSON string.

        Args:
            data: Parsed JSON data
            indent: Number of spaces for indentation

        Returns:
            Formatted JSON string
        """
        return json.dumps(data, indent=indent, ensure_ascii=False)

# =============================================================================
# Example Usage & Testing
# =============================================================================
def main():
    """Demonstrate JSON processing with the simplified interface."""

    # Test JSON with trailing commas and comments
    test_json = """
    {
      // This is a comment
      "uuid": "{6DE24C5A-7680-4A9C-AEDF-8460C886F143}",
      "name": "layout-0006-d--large",
      "type": "canvas",
      "info": {
        "ref-width": 3072,
        "ref-height": 1680,
        "zones": [
          {"X": 0, "Y": 0, "width": 591, "height": 420},
          {"X": 0, "Y": 0, "width": 591, "height": 1206},
          {"X": 0, "Y": 897, "width": 591, "height": 783},
          {"X": 0, "Y": 1206, "width": 591, "height": 474},
          {"X": 0, "Y": 0, "width": 591, "height": 1680},
          {"X": 0, "Y": 0, "width": 1109, "height": 420},
          {"X": 0, "Y": 0, "width": 1109, "height": 1206},
          {"X": 0, "Y": 897, "width": 1109, "height": 783},
          {"X": 0, "Y": 1206, "width": 1109, "height": 474},
          {"X": 0, "Y": 0, "width": 1111, "height": 1680},
          {"X": 1109, "Y": 0, "width": 805, "height": 420},
          {"X": 1109, "Y": 0, "width": 805, "height": 1206},
          {"X": 1109, "Y": 897, "width": 805, "height": 783},
          {"X": 1109, "Y": 1206, "width": 805, "height": 474},
          {"X": 1109, "Y": 0, "width": 805, "height": 1680},
          {"X": 1109, "Y": 0, "width": 1308, "height": 420},
          {"X": 1109, "Y": 0, "width": 1308, "height": 1206},
          {"X": 1109, "Y": 897, "width": 1308, "height": 783},
          {"X": 1109, "Y": 1206, "width": 1308, "height": 474},
          {"X": 1109, "Y": 0, "width": 1308, "height": 1680},
          {"X": 1914, "Y": 0, "width": 1158, "height": 420},
          {"X": 1914, "Y": 0, "width": 1158, "height": 1206},
          {"X": 1914, "Y": 897, "width": 1158, "height": 783},
          {"X": 1914, "Y": 1206, "width": 1158, "height": 474},
          {"X": 1914, "Y": 0, "width": 1158, "height": 1680},
          {"X": 2481, "Y": 0, "width": 591, "height": 420},
          {"X": 2481, "Y": 0, "width": 591, "height": 1206},
          {"X": 2481, "Y": 897, "width": 591, "height": 783},
          {"X": 2481, "Y": 1206, "width": 591, "height": 474},
          {"X": 2481, "Y": 0, "width": 591, "height": 1680},
          {"X": 0, "Y": 0, "width": 3072, "height": 397},
          {"X": 0, "Y": 1219, "width": 3072, "height": 461},
          {"X": 0, "Y": 1608, "width": 3072, "height": 72}, // trailing comma here
        ],
        "sensitivity-radius": 20, // another trailing comma
      }, // and here
    } // final trailing comma
    """

    print("=== JSON Processor Demo ===")
    try:
        # Parse the JSON with trailing commas and comments
        processor = JsonProcessor()
        data = processor.parse(test_json)

        print("✓ Successfully parsed JSON with trailing commas and comments")
        print(f"Layout name: {data['name']}")
        print(f"Canvas type: {data['type']}")
        print(f"Number of zones: {len(data['info']['zones'])}")
        print(f"Canvas dimensions: {data['info']['ref-width']}x{data['info']['ref-height']}")

        # Demonstrate formatting
        print("\n=== Formatted Output (first 3 zones) ===")
        sample_data = {
            "name": data["name"],
            "zones_sample": data["info"]["zones"][:3]
        }
        formatted = processor.format(sample_data)
        print(formatted)

    except Exception as e:
        print(f"✗ Processing failed: {e}")

if __name__ == "__main__":
    main()
